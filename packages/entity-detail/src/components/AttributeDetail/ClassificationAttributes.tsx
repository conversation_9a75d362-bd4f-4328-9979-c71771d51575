/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { memo, useState, useCallback, useRef, useEffect, useMemo } from 'react';
import {
    Box,
    Typography,
    styled,
    Button,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid,
    IconButton,
} from '@mui/material';
import get from 'lodash/get';
import { useDispatch, useSelector } from 'react-redux';
import {
    MainTooltip,
    commonMessages,
    PlusIcon,
    ExpandMoreIcon,
    DeleteIcon,
    checkingPermission,
} from '@glidesystems/styleguide';
import { useAuth } from '@glidesystems/caching-store';
import { EditIcon } from '../icons/icons';
import useToggle from '../../hooks/useToggle';
import EditPropertiesPanel from './EditProperties';
import { applyClassifications, onOpenModal, updateClassificationAttributes, updateDetailEntity } from '../../actions';
import sortBy from 'lodash/sortBy';
import isEmpty from 'lodash/isEmpty';
import { selectAppReducer } from '../../selectors';
import { filterAndSortAttributes } from '../../utils/helper';
import AddClassification from './AddClassification';
import AttributeRender from './AttributeRender';
import { EntityDetail, Schema } from '@glidesystems/api';

const Wrapper = styled(Box)(({ theme }) => ({
    margin: '16px',
    '& .headerSection': {
        display: 'flex',
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    '& .sectionContent': {
        marginTop: '8.5px',
    },
}));

export const StyledAccordion = styled(Accordion)(({ theme }) => ({
    boxShadow: 'none',
    border: `1px solid ${theme.palette.glide.stroke.normal.primary}`,
    borderRadius: '0 !important',
    margin: '8px 0',
    '& .allowPointer': {
        pointerEvents: 'auto',
        color: theme.palette.glide.text.normal.inverseTertiary,
    },
    '& .summary': {
        height: '38px',
        minHeight: '38px',
        flexDirection: 'row-reverse',
        paddingLeft: '4px',
        '& .MuiAccordionSummary-content': {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        '&.Mui-expanded': {
            height: '38px',
            minHeight: '38px',
            backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        },
    },
    '& .accordionDetails': {
        padding: '0 16px 12px 16px',
        marginTop: '8px',
    },
    '& .label': {
        marginLeft: '8px',
    },
}));

export const ControlledAccordion = ({ summary, details, label, ...props }) => {
    const [expanded, action] = useToggle(false);
    return (
        <StyledAccordion expanded={expanded}>
            <AccordionSummary
                {...props}
                className="summary"
                expandIcon={
                    <Box
                        sx={{
                            height: '40px',
                            width: '40px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                        className="allowPointer"
                        onClick={action.toggle}
                    >
                        <ExpandMoreIcon
                            sx={{
                                width: '16px',
                                height: '16px',
                            }}
                        />
                    </Box>
                }
            >
                <Typography variant="label2-med" sx={{ maxWidth: '80%' }} onClick={action.toggle}>
                    {label}
                </Typography>
                {summary}
            </AccordionSummary>
            <AccordionDetails className="accordionDetails">{details}</AccordionDetails>
        </StyledAccordion>
    );
};

export interface ClassificationAttributesProps {
    detailEntity: EntityDetail;
    classificationSchema: any;
    containerProps?: any;
    isReadOnly?: boolean;
    detailSchema: Schema;
    updateDetailEntityDispatch?: any;
}

const ClassificationAttributes = ({
    detailEntity,
    classificationSchema,
    containerProps = {},
    isReadOnly = false,
    detailSchema,
    updateDetailEntityDispatch = updateDetailEntity,
}: ClassificationAttributesProps) => {
    if (!detailEntity) {
        return null;
    }
    const userInfo = useAuth((state) => state.userInfo);
    const dispatch = useDispatch();
    const canClassifyPer = get(detailEntity, 'permissions.canClassify', false);
    const canModifyPer = get(detailEntity, 'permissions.canModify', false);
    const { flattenClassificationTree } = useSelector(selectAppReducer);
    const isLocked = get(detailEntity, 'properties.isLocked', false);
    const [openAddClassification, addClassificationToggle] = useToggle(false);

    const [openEditor, editorAction] = useToggle(false);
    const [selectedClassification, setSelectedClassification] = useState({
        name: '',
        attrs: [],
    });
    const appliedClassifications = useRef<string[]>([]);

    const canModify = useMemo(() => {
        if (isLocked) {
            return checkingPermission(detailEntity, userInfo, 'canModify');
        }
        return canModifyPer;
    }, [isLocked, canModifyPer, detailEntity, userInfo]);

    const getClassificationLabel = useCallback(
        (name: string) => {
            if (isEmpty(flattenClassificationTree)) {
                return '';
            }

            return flattenClassificationTree[name].path.join(' / ');
        },
        [flattenClassificationTree]
    );

    const canClassify = useMemo(() => {
        if (isLocked) {
            return checkingPermission(detailEntity, userInfo, 'canClassify');
        }
        return canClassifyPer;
    }, [isLocked, canClassifyPer, detailEntity, userInfo]);

    useEffect(() => {
        // To avoid closure state, since detailEntity is not a dependency of EditPropertiesPanel component
        appliedClassifications.current = detailEntity.classifications
            .filter((classification) => classification.permissions.canEdit)
            .map((classification) => classification.name);
    }, [detailEntity.classifications]);

    const onDelete = useCallback(
        (name: string) => {
            const deleteClassification = () => {
                const {
                    properties: { type: entityType, name: entityName },
                    id: entityId,
                } = detailEntity;
                const payload = appliedClassifications.current.filter((c) => c !== name);
                const successMessage = (
                    <span>
                        Successfully deleted classification <b>{name}</b> on the{' '}
                        <b>
                            {entityType} - {entityName}
                        </b>
                        .
                    </span>
                );
                dispatch(
                    applyClassifications(entityType, entityId, payload, updateDetailEntityDispatch, successMessage)
                );
            };

            dispatch(
                onOpenModal({
                    open: true,
                    title: 'Delete classification',
                    content: `Do you want to delete the classification <b>${name}</b>?`,
                    onModalNextAction: deleteClassification,
                    confirmColor: 'error',
                })
            );
        },
        [dispatch, detailEntity]
    );

    const handleEdit = useCallback(
        (name: string, attrs: any[]) => {
            setSelectedClassification({ name, attrs });
            editorAction.open();
        },
        [editorAction, setSelectedClassification]
    );

    const handleSave = useCallback(
        async (values) => {
            const payload = {
                attributes: values,
                classifications: appliedClassifications.current,
            };

            const successMessage = (
                <span>
                    Updated attributes for Classification <b>{selectedClassification.name}</b> successfully
                </span>
            );

            const status = await dispatch(
                updateClassificationAttributes(
                    detailEntity.id,
                    payload,
                    updateDetailEntityDispatch,
                    successMessage,
                    detailEntity.classifications
                )
            );

            if (status) {
                editorAction.close();
            }
        },
        [detailEntity]
    );

    const handleClose = useCallback(() => {
        setSelectedClassification({ name: '', attrs: [] });
        editorAction.close();
    }, []);

    const renderClassifications = () => {
        const { properties, classifications } = detailEntity;
        return sortBy(classifications, 'name').map(
            ({ name: classificationName, permissions: { canEdit, canRead } }) => {
                if (classificationSchema[classificationName]) {
                    const { attributes, classification } = classificationSchema[classificationName];
                    const { attributeOrder, name } = classification;
                    const dynamicAttributes = filterAndSortAttributes(attributes, attributeOrder, properties);
                    const label = getClassificationLabel(name);
                    return (
                        <ControlledAccordion
                            label={label}
                            key={`accordion-${name}`}
                            summary={
                                <>
                                    {!isReadOnly && (
                                        <div className="actionGroup allowPointer">
                                            <MainTooltip
                                                title={canModify && canEdit ? '' : commonMessages.noPermission}
                                            >
                                                <span>
                                                    <IconButton
                                                        sx={{ mr: '8px' }}
                                                        color="primary"
                                                        onClick={() => handleEdit(name, dynamicAttributes)}
                                                        disabled={!canModify || !canEdit}
                                                        size="small"
                                                    >
                                                        <EditIcon />
                                                    </IconButton>
                                                </span>
                                            </MainTooltip>
                                            <MainTooltip title={canClassify ? '' : commonMessages.noPermission}>
                                                <span>
                                                    <IconButton
                                                        sx={{ mr: '8px' }}
                                                        onClick={() => onDelete(name)}
                                                        size="small"
                                                        disabled={!canClassify}
                                                        color="error"
                                                    >
                                                        <DeleteIcon />
                                                    </IconButton>
                                                </span>
                                            </MainTooltip>
                                        </div>
                                    )}
                                </>
                            }
                            details={
                                <Grid container spacing={3}>
                                    {!canRead ? (
                                        <Grid item sx={{ wordWrap: 'break-word' }}>
                                            You do not have the permission to view the attributes for this
                                            classification
                                        </Grid>
                                    ) : (
                                        dynamicAttributes.map((attr: any) => {
                                            const isText = attr.type === 'TEXT';
                                            return (
                                                <Grid
                                                    item
                                                    {...(isText ? { xs: 12, md: 12 } : { xs: 6, md: 4 })}
                                                    key={`${name}-${attr.id}`}
                                                    sx={{ wordWrap: 'break-word' }}
                                                >
                                                    <AttributeRender sx={{ marginBottom: 0 }} attribute={attr} />
                                                </Grid>
                                            );
                                        })
                                    )}
                                </Grid>
                            }
                        />
                    );
                }
                return null;
            }
        );
    };

    if (detailSchema && detailSchema.classifications.length === 0) return <></>;

    return (
        <Wrapper {...containerProps}>
            <div className="headerSection">
                <Typography variant="ol1">Classification</Typography>
                {!isReadOnly && (
                    <MainTooltip title={canClassify ? '' : commonMessages.noPermission}>
                        <span>
                            <Button
                                disabled={!canClassify}
                                variant="ghost"
                                size="small"
                                onClick={addClassificationToggle.open}
                            >
                                Add Classification
                                <PlusIcon sx={{ ml: '8px' }} />
                            </Button>
                        </span>
                    </MainTooltip>
                )}
            </div>
            <div className="sectionContent">{renderClassifications()}</div>

            <EditPropertiesPanel
                open={openEditor}
                onClose={handleClose}
                entityType={selectedClassification.name}
                onSave={handleSave}
                attributes={selectedClassification.attrs}
                isClassification
                title={`Edit ${selectedClassification.name} Classification`}
            />
            <AddClassification
                open={openAddClassification}
                detailEntity={detailEntity}
                detailSchema={detailSchema}
                onClose={addClassificationToggle.close}
                updateDetailEntityDispatch={updateDetailEntityDispatch}
            />
        </Wrapper>
    );
};

export default memo(ClassificationAttributes);
