/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useMemo } from 'react';
import { useInternalNode, getSmoothStepPath, EdgeProps } from '@xyflow/react';
import { getEdgeParams } from '../../utils/diagram';

function FloatingEdge({ id, source, target, style = {}, data, markerEnd, selected }: EdgeProps) {
    const sourceNode = useInternalNode(source);
    const targetNode = useInternalNode(target);
    if (!sourceNode || !targetNode) {
        return null;
    }
    const { sx, sy, tx, ty, sourcePos, targetPos } = useMemo(
        () => getEdgeParams(sourceNode, targetNode),
        [sourceNode, targetNode]
    );

    const elkPoints = useMemo(() => (Array.isArray(data?.elkPoints) ? data.elkPoints : []), [data?.elkPoints]);
    const { edgePath, labelX, labelY } = useMemo(() => {
        if (elkPoints.length >= 2) {
            const points = elkPoints;
            const path = points.map((p, i) => `${i === 0 ? 'M' : 'L'} ${p.x} ${p.y}`).join(' ');
            const mid = Math.floor(points.length / 2);
            return { edgePath: path, labelX: points[mid].x, labelY: points[mid].y };
        }
        const [path, lx, ly] = getSmoothStepPath({
            sourceX: sx,
            sourceY: sy,
            sourcePosition: sourcePos,
            targetPosition: targetPos,
            targetX: tx,
            targetY: ty,
            borderRadius: 10,
            offset: 10,
        });
        return { edgePath: path, labelX: lx, labelY: ly };
    }, [elkPoints, sx, sy, tx, ty, sourcePos, targetPos]);

    return (
        <>
            {/* Visible edge */}
            <path
                id={id}
                className={`${data?.className || ''} react-flow__edge-path`}
                d={edgePath}
                strokeWidth={selected ? 3 : 2}
                strokeLinecap="round"
                markerEnd={markerEnd}
            />
        </>
    );
}

export default FloatingEdge;
