/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useCallback, useMemo } from 'react';
import { useAuth } from '@glidesystems/caching-store';
import { useDispatch, useSelector } from 'react-redux';
import { onOpenModal, reviseEntity } from '../../../actions';
import { commonMessages, RequestQuoteIcon, checkingPermission, notifySuccess } from '@glidesystems/styleguide';
import { PERMISSION } from '@glidesystems/api';
import { selectAppReducer } from '../../../selectors';
import ActionButton from './ActionButton';
import { useNavigate } from 'react-router-dom';
import { Action } from './types';

const ReviseAction = ({ isMenuItem = false, detailEntityProp, onMenuItemClick }: Action) => {
    const appSelector = useSelector(selectAppReducer);
    const detailEntity = detailEntityProp ?? appSelector.detailEntity;
    const userInfo = useAuth((state) => state.userInfo);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const handleRevise = useCallback(() => {
        dispatch(
            onOpenModal({
                open: true,
                title: 'Revise entity',
                content: `Do you want to revise this entity <b>${detailEntity?.properties?.name}</b>`,
                onModalNextAction: async () => {
                    const data: any = await dispatch(reviseEntity(detailEntity?.id));
                    if (data) {
                        notifySuccess('Entity revised successfully');
                        navigate(`/detail/${data.properties.type}/${data.id}/properties`);
                        return;
                    }
                },
                action: 'confirm',
            })
        );
    }, [detailEntity]);

    const actionPermission = useMemo(() => {
        const canRevise = checkingPermission(detailEntity, userInfo, PERMISSION.CAN_REVISE);
        return canRevise
            ? { error: false, message: 'Revise the entity' }
            : {
                  error: true,
                  message: commonMessages.noPermission,
              };
    }, [userInfo, detailEntity]);

    return (
        <ActionButton
            isMenuItem={isMenuItem}
            onClick={handleRevise}
            Icon={RequestQuoteIcon}
            actionPermission={actionPermission}
            onMenuItemClick={onMenuItemClick}
        >
            Revise
        </ActionButton>
    );
};

export default ReviseAction;
