/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import type { ColDef, GridApi, GridOptions, ICellRendererParams } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import {
    batchRequestBody,
    buildExactQuery,
    changeUrls,
    EntityDetail,
    entityUrls,
    fetch,
    PERMISSION,
    RelationType,
} from '@glidesystems/api';
import {
    AnimatedPage,
    commonMessages,
    DeleteIcon,
    EntityNameRenderer,
    Loading,
    notifyError,
    notifySuccess,
    PlusIcon,
    tableStyles,
} from '@glidesystems/styleguide';
import { Box, CircularProgress } from '@mui/material';
import get from 'lodash/get';
import keyBy from 'lodash/keyBy';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { PLANNED_CHANGE_ACTION } from '../../constants/planned-change';
import { EntityRelation } from '../../models/models';
import { selectAppReducer } from '../../selectors';
import PlannedChangeLayout from '../Layout/PlannedChangeLayout';
import AddRelations from '../Relations/AddRelations';
import { DropdownToolbarItem, ToolbarItem } from '../Relations/Toolbar';

interface RelationChangeSummaryResponse {
    relationName: string;
    fromEntityId: string;
    toEntityId: string;
    id: string;
    fromEntityProperties: Record<string, string>;
    toEntityProperties: Record<string, string>;
    actionType: PLANNED_CHANGE_ACTION.ADD_RELATION | PLANNED_CHANGE_ACTION.DELETE_RELATION;
}

const columnDefs: ColDef[] = [
    {
        field: 'relationName',
        headerName: 'Name',
        minWidth: 160,
        flex: 1,
        cellStyle: (params) => {
            if (params.data.actionType === PLANNED_CHANGE_ACTION.DELETE_RELATION) {
                return {
                    textDecoration: 'line-through',
                    color: 'red',
                };
            }
            if (params.data.actionType === PLANNED_CHANGE_ACTION.ADD_RELATION) {
                return {
                    color: '#52C41A',
                };
            }
            return {};
        },
        checkboxSelection: true,
        valueGetter: (params) => {
            const relationName = params.data.relationName;
            return params.context?.relationMap?.[relationName]?.displayName || relationName;
        },
        editable: false,
        filter: 'agSetColumnFilter',
        pinned: 'left',
    },
    {
        field: 'fromEntityProperties.name',
        headerName: 'From Entity',
        minWidth: 160,
        flex: 1,
        cellStyle: {},
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (props: ICellRendererParams<RelationChangeSummaryResponse>) => {
            const sx: Record<string, string> = {};
            if (props.data.actionType === PLANNED_CHANGE_ACTION.DELETE_RELATION) {
                sx.textDecoration = 'line-through';
                sx.color = 'red';
            } else if (props.data.actionType === PLANNED_CHANGE_ACTION.ADD_RELATION) {
                sx.color = '#52C41A';
            }
            return (
                <EntityNameRenderer
                    data={{ ...props.data?.fromEntityProperties, id: props.data?.fromEntityId }}
                    value={props.data?.fromEntityProperties?.name}
                    sx={sx}
                />
            );
        },
        autoHeight: true,
        wrapText: true,
    },
    {
        field: 'toEntityProperties.name',
        headerName: 'To Entity',
        minWidth: 160,
        flex: 1,
        cellStyle: {},
        editable: false,
        filter: 'agTextColumnFilter',
        cellRenderer: (props: ICellRendererParams<RelationChangeSummaryResponse>) => {
            const sx: Record<string, string> = {};
            if (props.data.actionType === PLANNED_CHANGE_ACTION.DELETE_RELATION) {
                sx.textDecoration = 'line-through';
                sx.color = 'red';
            } else if (props.data.actionType === PLANNED_CHANGE_ACTION.ADD_RELATION) {
                sx.color = '#52C41A';
            }
            return (
                <EntityNameRenderer
                    data={{ ...props.data?.toEntityProperties, id: props.data?.toEntityId }}
                    value={props.data?.toEntityProperties?.name}
                    sx={sx}
                />
            );
        },
        autoHeight: true,
        wrapText: true,
    },
    {
        field: 'actionType',
        headerName: 'Action Type',
        hide: true,
    },
];

const getRowId = (params) => {
    return params.data.fromEntityId + '_' + params.data.relationName + '_' + params.data.toEntityId;
};

const GRID_OPTIONS: GridOptions = {
    headerHeight: 34,
    loadingOverlayComponent: Loading,
    rowHeight: 34,
    enableGroupEdit: true,
    enableRangeSelection: true,
    columnDefs,
    defaultColDef: {
        sortable: true,
        resizable: true,
        filter: true,
        flex: 1,
        floatingFilter: false,
        enableRowGroup: false,
        enablePivot: false,
        autoHeight: true,
        wrapText: true,
    },
    undoRedoCellEditing: true,
    rowModelType: 'clientSide',
    getRowId,
    animateRows: true,
    rowSelection: 'multiple',
};

const RelationsPlan = ({ changedEntity }: { changedEntity: EntityDetail }) => {
    const { detailEntity, changedEntitySchema, showChangeItemsOnly } = useSelector(selectAppReducer);

    const changeControlledRelations = useMemo(() => {
        return changedEntitySchema?.relationTypes?.filter((relation) => relation.changeControlled);
    }, [changedEntitySchema]);
    const relationMap = keyBy(changeControlledRelations, 'name');
    const changeControlledRelationNames = changeControlledRelations?.map((relation) => relation.name) || [];
    const [selectedRelation, setSelectedRelation] = useState<RelationType>(undefined);
    const canConnect = get(changedEntity, ['permissions', 'canConnectAsFromSide']);
    const canDisconnect = get(changedEntity, ['permissions', 'canDisconnectAsFromSide']);
    const [selectedRows, setSelectedRows] = useState<RelationChangeSummaryResponse[]>([]);
    const [deleting, setDeleting] = useState(false);

    const gridRef = useRef<AgGridReact<RelationChangeSummaryResponse>>(null);

    const toggleShowChangesOnly = useCallback((value) => {
        if (gridRef.current?.api) {
            let currentFilter = gridRef.current.api.getFilterModel();
            if (value) {
                currentFilter = {
                    ...currentFilter,
                    actionType: {
                        filterType: 'set',
                        values: ['ADD_RELATION'],
                    },
                };
            } else {
                delete currentFilter.actionType;
            }
            gridRef.current.api.setFilterModel(currentFilter);
        }
    }, []);

    const TOOLBAR_ITEMS = useMemo(() => {
        return (changeControlledRelations || []).map((item) => {
            return {
                id: item.id,
                defaultMsg: item.displayName,
                onClick: () => setSelectedRelation(item),
            };
        });
    }, [changeControlledRelations]);

    const fetchRelations = useCallback(
        async (api: GridApi) => {
            if (changeControlledRelationNames.length === 0) {
                api.setRowData([]);
                return;
            }
            await Promise.all([
                fetch({
                    ...changeUrls.getRelationChangeSummary,
                    params: { entityId: detailEntity?.id },
                }),
                fetch({
                    ...entityUrls.getAllRelationsUnderEntity,
                    params: {
                        entityId: changedEntity?.id,
                    },
                    qs: {
                        relationNames: changeControlledRelationNames,
                    },
                }),
            ]).then(([changeSummary, relations]) => {
                const changeItems: RelationChangeSummaryResponse[] = changeSummary?.data?.data;
                const deletedChangeItemToChangeItemId = {};
                const deletedItems = changeItems
                    ?.filter((item) => item.actionType === PLANNED_CHANGE_ACTION.DELETE_RELATION)
                    ?.map((item) => {
                        deletedChangeItemToChangeItemId[
                            item.fromEntityId + '_' + item.relationName + '_' + item.toEntityId
                        ] = item.id;
                        return item.fromEntityId + '_' + item.relationName + '_' + item.toEntityId;
                    });
                const addedItems = changeItems?.filter(
                    (item) => item.actionType === PLANNED_CHANGE_ACTION.ADD_RELATION
                );
                const relationsUnderEntity: EntityRelation[] = relations?.data?.data;
                const rowData = [];
                relationsUnderEntity?.forEach((relation) => {
                    const deleteKey = changedEntity?.id + '_' + relation.name + '_' + relation.relation.id;
                    if (deletedItems.includes(deleteKey)) {
                        rowData.push({
                            fromEntityProperties: changedEntity?.properties,
                            toEntityProperties: relation.relation,
                            relationName: relation.name,
                            actionType: PLANNED_CHANGE_ACTION.DELETE_RELATION,
                            fromEntityId: changedEntity?.id,
                            toEntityId: relation.relation.id,
                            id: deletedChangeItemToChangeItemId[deleteKey],
                        });
                    } else {
                        rowData.push({
                            fromEntityProperties: changedEntity?.properties,
                            toEntityProperties: relation.relation,
                            relationName: relation.name,
                            fromEntityId: changedEntity?.id,
                            toEntityId: relation.relation.id,
                        });
                    }
                });
                rowData.push(...addedItems);
                api.setRowData(rowData);
            });
        },
        [changeControlledRelationNames, changedEntity, detailEntity]
    );

    const onDeleteChanges = useCallback(() => {
        setDeleting(true);
        const changeItemsToDelete = selectedRows
            .filter(
                (row) =>
                    row.actionType === PLANNED_CHANGE_ACTION.DELETE_RELATION ||
                    row.actionType === PLANNED_CHANGE_ACTION.ADD_RELATION
            )
            .map((row) => row.id);
        const itemsToCreateDeleteRelationChangeItems = selectedRows.filter((row) => !row.actionType);
        const requests = [];
        if (itemsToCreateDeleteRelationChangeItems.length > 0) {
            requests.push(
                ...itemsToCreateDeleteRelationChangeItems.map((row) =>
                    fetch({
                        ...changeUrls.deleteRelationPlan,
                        params: { entityId: detailEntity?.id },
                        data: {
                            payload: {
                                fromEntityId: changedEntity?.id,
                                toEntityId: row.toEntityId,
                                relationName: row?.relationName,
                            },
                            entityId: changedEntity?.id,
                        },
                        skipToast: true,
                    })
                )
            );
        }
        const batchUrls = [];
        if (changeItemsToDelete.length > 0) {
            batchUrls.push(
                ...changeItemsToDelete.map((id) => ({
                    subParams: { entityId: id },
                    url: `/entity/${id}?softDelete=false`,
                    method: entityUrls.deleteEntity.method,
                }))
            );
        }
        const batchEntityDetails = batchRequestBody(
            changeUrls.deleteRelationPlan.method,
            changeUrls.deleteRelationPlan.url,
            batchUrls
        );
        if (batchUrls.length > 0) {
            requests.push(
                fetch({
                    ...entityUrls.batchRequest,
                    data: batchEntityDetails,
                    skipToast: true,
                })
            );
        }
        Promise.all(requests)
            .then(() => {
                notifySuccess('Changes deleted successfully');
                gridRef.current?.api.deselectAll();
                fetchRelations(gridRef.current?.api).then(() => gridRef.current.api.refreshCells({ force: true }));
            })
            .catch(() => {
                notifyError('An error occured while updating the planned change');
            })
            .finally(() => {
                setDeleting(false);
            });
    }, [selectedRows, detailEntity?.id, fetchRelations, changedEntity]);

    useEffect(() => {
        toggleShowChangesOnly(showChangeItemsOnly);
    }, [showChangeItemsOnly]);

    return (
        <PlannedChangeLayout
            ToolbarComponent={
                <>
                    <DropdownToolbarItem
                        msg={canConnect ? null : { message: commonMessages.noPermission }}
                        items={TOOLBAR_ITEMS}
                        icon={<PlusIcon />}
                    />
                    {selectedRows.length > 0 && (
                        <ToolbarItem
                            msg={canDisconnect ? null : { message: commonMessages.noPermission }}
                            defaultMsg="Delete Changes from PlannedChange"
                            icon={
                                deleting ? (
                                    <CircularProgress sx={{ color: '#334466' }} size={14} />
                                ) : (
                                    <DeleteIcon style={{ color: 'red', width: '16px', height: '16px' }} />
                                )
                            }
                            onClick={onDeleteChanges}
                        />
                    )}
                </>
            }
        >
            <AnimatedPage style={{ height: '100%', width: '100%' }}>
                <Box
                    sx={{
                        flexGrow: 1,
                        ...tableStyles,
                        height: {
                            xs: '500px',
                            md: '100%',
                        },
                        width: '100%',
                    }}
                >
                    <Box
                        id="planned-change-attributes"
                        sx={{
                            height: '100%',
                            width: '100%',
                            borderRadius: 8,
                            backgroundColor: '#F9F9F9',
                        }}
                        className="ag-theme-alpine"
                    >
                        <AgGridReact
                            gridOptions={GRID_OPTIONS}
                            ref={gridRef}
                            context={{
                                relationMap: relationMap,
                            }}
                            onGridReady={async ({ api }) => {
                                fetchRelations(api);
                            }}
                            onSelectionChanged={(params) => {
                                setSelectedRows(params.api.getSelectedRows());
                            }}
                        />
                    </Box>
                </Box>
                <AddRelations
                    title={`Add ${selectedRelation?.displayName}`}
                    open={selectedRelation !== undefined}
                    onClose={() => setSelectedRelation(undefined)}
                    entityType={selectedRelation?.toEntityType}
                    extraFilters={buildExactQuery('permissions', PERMISSION.CAN_CONNECT_AS_TO_SIDE)}
                    onSubmit={async (
                        values: Record<string, any>,
                        selectedRows: Array<Record<string, any>>,
                        successCallback
                    ) => {
                        fetch({
                            ...changeUrls.addRelationPlan,
                            params: { entityId: detailEntity?.id },
                            data: {
                                payload: {
                                    fromEntityId: changedEntity?.id,
                                    toEntityId: selectedRows[0].id,
                                    relationName: selectedRelation?.name,
                                },
                                entityId: changedEntity?.id,
                            },
                        })
                            .then((res) => {
                                fetchRelations(gridRef.current?.api);
                            })
                            .finally(() => {
                                successCallback();
                            });
                    }}
                    includeRevision={false}
                    draggable={false}
                    placeholder="Search for Items..."
                    rowSelection="single"
                    relationSchema={{
                        relationType: selectedRelation,
                    }}
                    excludedIds={gridRef.current?.api
                        ?.getRenderedNodes()
                        .filter(
                            (node) =>
                                node.data.actionType !== PLANNED_CHANGE_ACTION.DELETE_RELATION &&
                                node.data.relationName === selectedRelation?.name
                        )
                        .map((node) => node.data.toEntityId)}
                />
            </AnimatedPage>
        </PlannedChangeLayout>
    );
};

export default RelationsPlan;
