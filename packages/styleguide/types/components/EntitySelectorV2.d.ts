export interface EntitySelectOption {
    value: string;
    label: string;
    type: string;
    email?: string;
}
declare const EntitySelectorV2: ({ label, value, entityType, isMulti, onChange, isRequired, error, helperText, onBlur, }: {
    label: string;
    value: EntitySelectOption;
    entityType: string;
    isMulti?: boolean;
    onChange: (value: EntitySelectOption | EntitySelectOption[]) => void;
    isRequired?: boolean;
    error?: boolean;
    helperText?: string;
    onBlur?: () => void;
}) => import("react/jsx-runtime").JSX.Element;
export default EntitySelectorV2;
//# sourceMappingURL=EntitySelectorV2.d.ts.map