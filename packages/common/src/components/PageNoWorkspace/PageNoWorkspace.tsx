/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect } from 'react';
import { Typography } from '@mui/material';
import NoWorkspaceImage from '../../images/no-workspace.png';
import './styles.css';

const PageNoWorkspace = () => {
    useEffect(() => {
        document.documentElement.classList.add('disable-interactions');

        return () => {
            document.documentElement.classList.remove('disable-interactions');
        };
    }, []);

    return (
        <div className="no-workspace-container">
            <div className="no-workspace-content">
                <img src={NoWorkspaceImage} alt="No Workspace" className="no-workspace-image" draggable="false" />
                <Typography
                    sx={{
                        fontWeight: 500,
                        fontSize: 18,
                    }}
                >
                    No Workspace Assigned!
                </Typography>
                <Typography
                    sx={{
                        fontWeight: 400,
                        fontSize: 16,
                    }}
                >
                    You are currently not assigned to any Workspace. To begin using the Glide-SYSLM Platform, please
                    contact your system administrator to request Workspace access.
                </Typography>
            </div>
        </div>
    );
};

export default PageNoWorkspace;
