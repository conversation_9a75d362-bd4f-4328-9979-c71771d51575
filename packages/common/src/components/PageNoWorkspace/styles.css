.no-workspace-container {
    padding-top: 52px;
    z-index: 1203;
    height: 100lvh;
    display: flex;
    top: 0;
    left: 0;
    bottom: 0;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.no-workspace-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    text-align: center;
    max-width: 720px;
    padding: 2rem;
}

.no-workspace-image {
    width: 50%;
    height: auto;
}

html.disable-interactions {
    cursor: not-allowed !important;
}

html.disable-interactions .dim-when-disabled {
    opacity: 0.5;
}

html.disable-interactions .none-pe-when-disabled {
    pointer-events: none !important;
}
