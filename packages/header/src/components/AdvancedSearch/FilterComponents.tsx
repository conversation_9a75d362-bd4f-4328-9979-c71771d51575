/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import {
    Autocomplete,
    Box,
    Button,
    Checkbox,
    FormControl,
    FormHelperText,
    IconButton,
    MenuItem,
    Select,
    SxProps,
    TextField,
    Typography,
} from '@mui/material';
import {
    EntitySelect,
    formatDate,
    formatSystemDateTime,
    GroupIcon,
    PlusIcon,
    TrashIcon,
} from '@glidesystems/styleguide';
import { QUERY, AttributeType, Attribute } from '@glidesystems/api';
import { FieldArray, FormikProps } from 'formik';
import { SchemaWithLifeCycleDetail, useGlobalConfig } from '@glidesystems/caching-store';
import get from 'lodash/get';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { parse as dateFnsParse } from 'date-fns';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
    DATA_TYPE_OPERATORS,
    DEFAULT_FILTER_OPERATORS,
    DEFAULT_FILTER_VALUE,
    DEFAULT_GROUP_FILTER_VALUE,
    EMPTY_FILTER_VALUE,
    FILTER_OPTION_LABEL,
    FILTER_OPTIONS,
    GROUP_OPERATORS,
    INITIAL_CONDITION,
    OPERATORS_BY_TYPE,
} from '../../constants';
import {
    getDataType,
    getEnumRange,
    isArrayAttribute,
    isAttributeArrayType,
    isAttributeDateType,
    isAttributeFilter,
    isClassificationFilter,
    isError,
    isGroupFilter,
    isLifecycleStateFilter,
    isRelationAttributeFilter,
    isRelationFilter,
} from '../../utils/advancedSearchHelper';
import * as Yup from 'yup';
import RelationFilter from './RelationFilter';
import LifecycleStateFilter from './LifecycleStateFilter';
import AttributeFilter from './AttributeFilter';
import { DateArrayInput, MultiValueInput } from '../ArrayAttribute';
import DateRangePickerInput from '../DateRangePickerInput/DateRangePickerInput';
import StyledPaper from '../CreatePaletteV2/StyledPaper';
import ClassificationFilter from './ClassificationFilter';
import RelationAttributeFilter from './RelationAttributeFilter';

export const MENU_PROPS: any = {
    anchorOrigin: {
        vertical: 'bottom',
        horizontal: 'left',
    },
    transformOrigin: {
        vertical: 'top',
        horizontal: 'left',
    },
    PaperProps: {
        style: {
            maxHeight: '320px',
        },
    },
};

type OperatorOption = { label: string; value: string };

const RELATIVE_CURRENT_REGEX = /^\$CURRENT\s-\s\d+$/;
export const RELATIVE_OPERATOR_VALUE = 'RELATIVE';
const RELATIVE_YESTERDAY_VALUE = '$CURRENT - 1';
const RELATIVE_WEEK_VALUE = '$CURRENT - 7';
const RELATIVE_MONTH_VALUE = '$CURRENT - 30';
const RELATIVE_YEAR_VALUE = '$CURRENT - 365';
type RelativePreset = 'yesterday' | 'week' | 'month' | 'year' | 'custom';

const RELATIVE_PRESET_BY_DAYS: Record<number, RelativePreset> = {
    1: 'yesterday',
    7: 'week',
    30: 'month',
    365: 'year',
};

const RELATIVE_PRESET_LABELS: Record<RelativePreset, string> = {
    yesterday: 'Yesterday',
    week: 'Last Week',
    month: 'Last Month',
    year: 'Last Year',
    custom: 'Custom (Last N days)',
};

const RELATIVE_PRESETS: readonly RelativePreset[] = ['yesterday', 'week', 'month', 'year', 'custom'] as const;

const isRelativePreset = (preset?: string): preset is RelativePreset =>
    preset === 'week' || preset === 'month' || preset === 'year' || preset === 'custom';

const getRelativePresetValue = (relativeDays: number, currentPreset?: string): RelativePreset => {
    if (isRelativePreset(currentPreset)) {
        return currentPreset;
    }
    return RELATIVE_PRESET_BY_DAYS[relativeDays] ?? 'custom';
};

const formatRelativePresetLabel = (preset: RelativePreset): string => RELATIVE_PRESET_LABELS[preset];

export const getDefaultValueForAttribute = (attribute: Attribute) => {
    if (attribute) {
        switch (attribute.type) {
            case AttributeType.BOOLEAN:
                return false;
            case AttributeType.INTEGER:
            case AttributeType.FLOAT:
            case AttributeType.LONG:
            case AttributeType.STRING:
            case AttributeType.TEXT:
            case AttributeType.DATE:
            case AttributeType.DATE_TIME:
                return '';
        }
    }
    return '';
};

export const getDefaultConditionForAttribute = (attribute: Attribute) => {
    if (attribute) {
        switch (attribute.type) {
            case AttributeType.BOOLEAN:
                return QUERY.EXACT;
            case AttributeType.INTEGER:
            case AttributeType.FLOAT:
            case AttributeType.LONG:
                return QUERY.EQUAL;
            case AttributeType.STRING:
            case AttributeType.TEXT:
                if (getDataType(attribute) || getEnumRange(attribute)) {
                    return QUERY.EXACT;
                }
                return QUERY.CONTAINS;
            case AttributeType.DATE:
            case AttributeType.DATE_TIME:
                return '';
            case AttributeType.DATE_ARRAY:
            case AttributeType.STRING_ARRAY:
            case AttributeType.DATE_TIME_ARRAY:
            case AttributeType.INTEGER_ARRAY:
            case AttributeType.FLOAT_ARRAY:
                return QUERY.CONTAINS;
        }
    }
    return '';
};
const handleFilterTypeChange = (formProps: FormikProps<AdvancedFilterFormProps>, name: string, fieldPrefix: string) => {
    return (e) => {
        const selectedFilterType: FilterType = e.target.value;

        let fieldValue;

        switch (selectedFilterType) {
            case 'relation':
                fieldValue = {
                    ...INITIAL_CONDITION,
                    filterType: 'relation',
                };
                break;

            case 'relationExists':
                fieldValue = {
                    filterType: 'relationExists',
                    operator: QUERY.IS_NON_NULL,
                    values: {
                        field: `${fieldPrefix}id`,
                    },
                };
                break;

            case 'relationNotExists':
                fieldValue = {
                    filterType: 'relationNotExists',
                    operator: QUERY.IS_NULL,
                    values: {
                        field: `${fieldPrefix}id`,
                    },
                };
                break;

            case 'classification':
                fieldValue = {
                    filterType: 'classification',
                    operator: QUERY.IN,
                    values: {
                        field: `${fieldPrefix}relation.CLASSIFIED_BY.name`,
                    },
                };
                break;

            default:
                fieldValue = {
                    filterType: selectedFilterType,
                    values: EMPTY_FILTER_VALUE,
                };
                break;
        }

        formProps.setFieldValue(name, fieldValue, false);
    };
};

export const FilterOperator = ({
    name,
    value,
    formProps,
    type,
    dataType,
    enumRange,
}: {
    name: string;
    value: any;
    formProps: FormikProps<AdvancedFilterFormProps>;
    type: any;
    dataType?: string;
    enumRange?: string[];
}) => {
    const operatorName = `${name}.operator`;
    const operators: readonly OperatorOption[] =
        dataType && !isArrayAttribute(type) ? DATA_TYPE_OPERATORS : OPERATORS_BY_TYPE[type] || DEFAULT_FILTER_OPERATORS;

    const isDateType = isAttributeDateType(type);
    const isArrayType = isAttributeArrayType(type);

    const currentValue = get(formProps.values, `${name}.values.value`);
    const relativeMatch = typeof currentValue === 'string' && RELATIVE_CURRENT_REGEX.test(currentValue || '');
    const relativeDays = relativeMatch ? Number(String(currentValue).replace('$CURRENT - ', '').trim()) : null;
    const renderLabel = () => {
        if (isDateType && relativeMatch) {
            return 'Relative';
        }
        return value ? operators.find((option) => option.value === value)?.label : 'Select Operator';
    };
    const operatorSelectValue = isDateType && relativeMatch ? RELATIVE_OPERATOR_VALUE : value;
    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FormControl error={isError(formProps, operatorName)}>
                <Select
                    size="small"
                    name={operatorName}
                    MenuProps={MENU_PROPS}
                    value={operatorSelectValue}
                    inputProps={{ 'aria-label': 'Operator' }}
                    onChange={(e) => {
                        const selected = e.target.value;
                        if (isDateType && selected === RELATIVE_OPERATOR_VALUE) {
                            formProps.setFieldValue(operatorName, isArrayType ? QUERY.CONTAINS : QUERY.GTE, false);
                            formProps.setFieldValue(`${name}.values.value`, RELATIVE_WEEK_VALUE, false);
                            formProps.setFieldValue(`${name}.values._relativePreset`, 'week', false);
                            return;
                        }
                        formProps.handleChange(e);
                        formProps.setFieldValue(`${name}.values.value`, null);
                    }}
                    displayEmpty
                    renderValue={renderLabel}
                >
                    {operators.map((filter: OperatorOption) => (
                        <MenuItem key={filter.value} value={filter.value}>
                            {filter.label}
                        </MenuItem>
                    ))}
                    {isDateType && <MenuItem value={RELATIVE_OPERATOR_VALUE}>Relative</MenuItem>}
                </Select>
                {isError(formProps, operatorName) && (
                    <FormHelperText error={isError(formProps, operatorName)}>
                        {get(formProps.errors, operatorName)}
                    </FormHelperText>
                )}
            </FormControl>

            {isDateType && relativeMatch && (
                <Box sx={{ display: 'inline-flex', alignItems: 'center', gap: '8px' }}>
                    <FormControl size="small">
                        <Select
                            size="small"
                            value={getRelativePresetValue(
                                relativeDays,
                                get(formProps.values, `${name}.values._relativePreset`) as string | undefined
                            )}
                            onChange={(e) => {
                                const preset = e.target.value;
                                formProps.setFieldValue(`${name}.values._relativePreset`, preset, false);
                                if (preset === 'week') {
                                    formProps.setFieldValue(`${name}.values.value`, RELATIVE_WEEK_VALUE, false);
                                } else if (preset === 'month') {
                                    formProps.setFieldValue(`${name}.values.value`, RELATIVE_MONTH_VALUE, false);
                                } else if (preset === 'year') {
                                    formProps.setFieldValue(`${name}.values.value`, RELATIVE_YEAR_VALUE, false);
                                } else if (preset === 'yesterday') {
                                    formProps.setFieldValue(`${name}.values.value`, RELATIVE_YESTERDAY_VALUE, false);
                                }
                            }}
                            MenuProps={MENU_PROPS}
                            renderValue={(selected) => formatRelativePresetLabel(selected as RelativePreset)}
                        >
                            {RELATIVE_PRESETS.map((preset) => (
                                <MenuItem key={preset} value={preset}>
                                    {RELATIVE_PRESET_LABELS[preset]}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    {get(formProps.values, `${name}.values._relativePreset`) === 'custom' && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <TextField
                                size="small"
                                type="number"
                                value={relativeDays || 7}
                                onChange={(e) => {
                                    const n = Math.max(1, Math.floor(Number(e.target.value || 1)));
                                    formProps.setFieldValue(`${name}.values.value`, `$CURRENT - ${n}`, false);
                                }}
                                inputProps={{ min: 1, step: 1 }}
                                sx={{ width: 100 }}
                                error={isError(formProps, `${name}.values.value`)}
                                helperText={
                                    isError(formProps, `${name}.values.value`)
                                        ? get(formProps.errors, `${name}.values.value`)
                                        : ''
                                }
                            />
                            <Typography variant="bo2">days</Typography>
                        </Box>
                    )}
                </Box>
            )}
        </Box>
    );
};

export type RelationRowData = {
    id: string;
    path: string[];
    displayName: string;
    name: string;
    entityType: string;
    isLoaded: boolean;
    relationQuery: string;
    fromEntityType?: string;
    toEntityType?: string;
};
export type FilterType =
    | 'relation'
    | 'relationAttribute'
    | 'lifecycleState'
    | 'attribute'
    | 'group'
    | 'relationExists'
    | 'relationNotExists'
    | 'classification';
export type Condition = {
    operator: string | any;
    values: { field: string; value: any } | Condition[];
    filterType?: FilterType;
};
export type AdvancedFilterFormProps = {
    condition: Condition;
};
const filterTypeSchema = Yup.string()
    .oneOf([
        'attribute',
        'lifecycleState',
        'relation',
        'relationExists',
        'relationNotExists',
        'classification',
        'relationAttribute',
    ])
    .required('Filter type is required');

export const filterSchema = Yup.object({
    operator: Yup.string().required('Operator is required'),
    values: Yup.mixed().when('operator', {
        is: (operator) => operator !== QUERY.IS_NULL && operator !== QUERY.IS_NON_NULL,
        then: Yup.object({
            field: Yup.string().required('Field is required'),
            value: Yup.mixed().required('Value is required'),
        }).required('Value is required'),
        otherwise: Yup.mixed().notRequired(),
    }),
    filterType: filterTypeSchema,
});

export const conditionSchema = Yup.lazy((value) => {
    if (value?.filterType === 'group' || value?.filterType === 'relation') {
        return groupSchema;
    }
    if (['relationExists', 'relationNotExists'].includes(value?.filterType)) {
        return Yup.mixed().notRequired();
    }
    return filterSchema;
});

// Schema for group filters (recursive)
export const groupSchema = Yup.object({
    operator: Yup.string().oneOf([QUERY.AND, QUERY.OR]).required('Operator is required'),
    values: Yup.array()
        .of(conditionSchema)
        .min(1, 'Group Filter must have at least one value')
        .required('Values are required'),
    filterType: Yup.string().oneOf(['group', 'relation']).required('Filter type is required'),
});

export const classificationSchema = Yup.object({
    operator: Yup.string().oneOf([QUERY.IN, QUERY.NOT_IN]).required('Operator is required'),
    values: Yup.array()
        .of(conditionSchema)
        .min(1, 'Classification Filter must have at least one value')
        .required('Values are required'),
    filterType: Yup.string().oneOf(['classification']).required('Filter type is required'),
});

// Root schema with the condition key
export const validationSchema = Yup.object({
    condition: Yup.object({
        operator: Yup.string().oneOf([QUERY.AND, QUERY.OR]).required('Operator is required'),
        values: Yup.array()
            .of(conditionSchema)
            .min(1, 'Group Filter must have at least one element')
            .required('Values is required'),
    }).required('Condition is required'),
});

export const Group = ({
    name,
    schemaDetail,
    formProps,
    sx = {},
    fieldPrefix = '',
    filterOptions,
    selectedRelation,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
    sx?: SxProps;
    fieldPrefix?: string;
    filterOptions?: any[];
    selectedRelation?: RelationRowData;
}) => {
    const { values, errors } = formProps;
    const filter = get(values, name);
    const onAddFilter = () => {
        const newValues = [...get(filter, 'values', []), { ...DEFAULT_FILTER_VALUE }];
        formProps.setFieldValue(`${name}.values`, newValues, false);
    };
    const onAddGroupFilter = () => {
        const newValues = [...get(filter, 'values', [{ ...DEFAULT_FILTER_VALUE }]), { ...DEFAULT_GROUP_FILTER_VALUE }];
        formProps.setFieldValue(`${name}.values`, newValues, false);
    };
    return (
        <Box
            sx={{
                background: (theme) => theme.palette.glide.background.normal.inversePrimary,
                padding: '8px',
                borderRadius: '4px',
                border: (theme) => `1px solid ${theme.palette.glide.stroke.normal.primary}`,
                marginTop: '8px',
                position: 'relative',
                width: '100%',
                ...sx,
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    gap: '8px',
                    mt: '8px',
                }}
            >
                <Box>
                    <FormControl error={isError(formProps, `${name}.filterType`)}>
                        <Select
                            size="small"
                            name={`${name}.operator`}
                            value={filter?.operator}
                            MenuProps={MENU_PROPS}
                            onChange={formProps.handleChange}
                        >
                            {GROUP_OPERATORS.map(({ value, label }) => (
                                <MenuItem value={value} key={`op-${value}`}>
                                    {label}
                                </MenuItem>
                            ))}
                        </Select>
                        <FormHelperText>{get(errors, `${name}.filterType`)}</FormHelperText>
                    </FormControl>
                </Box>
                <Box sx={{ display: 'flex', gap: '8px', flexDirection: 'column', flexGrow: 1 }}>
                    <FieldArray
                        name={`${name}.values`}
                        render={(arrayHelpers) => {
                            return get(filter, 'values', []).map((value, idx) => {
                                if (isGroupFilter(value)) {
                                    return (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                gap: '4px',
                                                justifyContent: 'space-between',
                                                width: '100%',
                                            }}
                                            key={`${name}.values[${idx}]`}
                                        >
                                            <Group
                                                name={`${name}.values[${idx}]`}
                                                schemaDetail={schemaDetail}
                                                formProps={formProps}
                                            />
                                            <IconButton size="small" onClick={() => arrayHelpers.remove(idx)}>
                                                <TrashIcon />
                                            </IconButton>
                                        </Box>
                                    );
                                }
                                return (
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            gap: '4px',
                                            justifyContent: 'space-between',
                                        }}
                                        key={`${name}.values[${idx}]`}
                                    >
                                        <Filter
                                            name={`${name}.values[${idx}]`}
                                            schemaDetail={schemaDetail}
                                            formProps={formProps}
                                            fieldPrefix={fieldPrefix}
                                            filterOptions={filterOptions}
                                            selectedRelation={selectedRelation}
                                        />
                                        <IconButton size="small" onClick={() => arrayHelpers.remove(idx)}>
                                            <TrashIcon />
                                        </IconButton>
                                    </Box>
                                );
                            });
                        }}
                    />
                </Box>
            </Box>
            {isError(formProps, `${name}.values`) && (
                <FormHelperText error={isError(formProps, `${name}.values`)}>
                    {get(errors, `${name}.values`)}
                </FormHelperText>
            )}
            <Box sx={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
                <Button size="small" endIcon={<PlusIcon />} variant="contained-blue" onClick={onAddFilter}>
                    Add Filter
                </Button>
                <Button size="small" endIcon={<GroupIcon />} variant="contained-blue" onClick={onAddGroupFilter}>
                    Add Group Filter
                </Button>
            </Box>
        </Box>
    );
};

const parseArrayValue = (value: string | any[]) => {
    if (Array.isArray(value)) {
        return value;
    }
    if (typeof value === 'string' && value) {
        return value.split(',').map((item) => item.trim());
    }
    return [];
};
export const FilterValue = ({
    name,
    formProps,
    attributeSchemaType,
    handleChange,
    filterOperator,
    options = [],
    autocompleteProps = {},
    dataType,
    enumRange,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    attributeSchemaType: string;
    handleChange: any;
    filterOperator: any;
    options?: any[];
    autocompleteProps?: any;
    dataType?: string;
    enumRange?: string[];
}) => {
    const { errors, values } = formProps;
    if ([QUERY.IN, QUERY.NOT_IN].includes(filterOperator) || (enumRange && filterOperator === QUERY.EXACT)) {
        switch (attributeSchemaType) {
            case AttributeType.INTEGER_ARRAY:
            case AttributeType.FLOAT_ARRAY:
            case AttributeType.STRING_ARRAY:
                return (
                    <MultiValueInput
                        fullWidth={false}
                        type={attributeSchemaType}
                        displayName=""
                        sx={{
                            '& .MuiOutlinedInput-root': { minWidth: 240 },
                            '& button': { width: 24, height: 24 },
                            '& .MuiAutocomplete-input': {
                                paddingLeft: '36px',
                            },
                        }}
                        value={parseArrayValue(get(values, name))}
                        onChange={(value) => formProps.setFieldValue(name, value)}
                        error={isError(formProps, name) ? get(errors, name) : ''}
                        onBlur={() => {
                            formProps.setFieldTouched(name);
                        }}
                        enumRange={enumRange}
                    />
                );
            case AttributeType.DATE_ARRAY:
            case AttributeType.DATE_TIME_ARRAY:
                return (
                    <DateArrayInput
                        fullWidth={false}
                        isDateTime={attributeSchemaType === AttributeType.DATE_TIME_ARRAY}
                        displayName=""
                        value={parseArrayValue(get(values, name))}
                        onChange={(value) => formProps.setFieldValue(name, value)}
                        error={isError(formProps, name) ? get(errors, name) : ''}
                        onBlur={() => {
                            formProps.setFieldTouched(name);
                        }}
                    />
                );
            default:
                return (
                    <Autocomplete
                        PaperComponent={StyledPaper}
                        multiple={![QUERY.EQUAL, QUERY.EXACT].includes(filterOperator)}
                        size="small"
                        freeSolo
                        options={options}
                        value={parseArrayValue(get(values, name))}
                        sx={{
                            '& .MuiOutlinedInput-root': { minWidth: 240 },
                            '& button': { width: 24, height: 24 },
                        }}
                        onChange={(_, newValue: any) => formProps.setFieldValue(name, newValue)}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                size="small"
                                InputLabelProps={{ shrink: true }}
                                error={isError(formProps, name)}
                                helperText={isError(formProps, name) ? get(errors, name) : ''}
                            />
                        )}
                        {...autocompleteProps}
                    />
                );
        }
    }
    if ([QUERY.BETWEEN].includes(filterOperator)) {
        switch (attributeSchemaType) {
            case AttributeType.DATE:
            case AttributeType.DATE_TIME:
                return (
                    <>
                        <DateRangePickerInput
                            value={get(values, name)}
                            onChange={(newValue) => formProps.setFieldValue(name, newValue)}
                        />
                        {isError(formProps, name) && <FormHelperText error>{get(errors, name)}</FormHelperText>}
                    </>
                );
            default:
                return (
                    <Box sx={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                        <TextField
                            size="small"
                            name={`${name}.0`}
                            type="number"
                            value={get(values, `${name}.0`)}
                            placeholder="Input value"
                            onChange={handleChange}
                            error={isError(formProps, name)}
                            helperText={isError(formProps, name) ? get(errors, name) : ''}
                        />
                        <Typography variant="bo2">And</Typography>
                        <TextField
                            size="small"
                            name={`${name}.1`}
                            type="number"
                            value={get(values, `${name}.1`)}
                            placeholder="Input value"
                            onChange={handleChange}
                            error={isError(formProps, name)}
                            helperText={isError(formProps, name) ? get(errors, name) : ''}
                        />
                    </Box>
                );
        }
    }
    switch (attributeSchemaType) {
        case AttributeType.STRING:
        case AttributeType.STRING_ARRAY:
        case AttributeType.TEXT:
            if (dataType) {
                return (
                    <Box sx={{ minWidth: 200 }}>
                        <EntitySelect
                            entityType={dataType}
                            onChange={(newValue) => {
                                handleChange({
                                    target: { name, value: newValue.value },
                                });
                            }}
                            defaultValue={get(values, name)}
                        />
                        {isError(formProps, name) && <FormHelperText error>{get(errors, name)}</FormHelperText>}
                    </Box>
                );
            }
            return (
                <TextField
                    size="small"
                    name={name}
                    value={get(values, name)}
                    placeholder="Input value"
                    onChange={handleChange}
                    error={isError(formProps, name)}
                    helperText={isError(formProps, name) ? get(errors, name) : ''}
                />
            );
        case AttributeType.BOOLEAN:
            return (
                <FormControl error={isError(formProps, name)}>
                    <Checkbox
                        name={name}
                        checked={get(values, name, false)}
                        onChange={(_, checked) => handleChange({ target: { name, value: checked } })}
                        color="secondary"
                        sx={{
                            width: 'min-content',
                        }}
                    />
                    {isError(formProps, name) && <FormHelperText error>{get(errors, name)}</FormHelperText>}
                </FormControl>
            );
        case AttributeType.INTEGER:
        case AttributeType.FLOAT:
        case AttributeType.LONG:
        case AttributeType.INTEGER_ARRAY:
        case AttributeType.FLOAT_ARRAY:
            return (
                <TextField
                    size="small"
                    name={name}
                    type="number"
                    value={get(values, name)}
                    placeholder="Input value"
                    onChange={handleChange}
                    error={isError(formProps, name)}
                    helperText={isError(formProps, name) ? get(errors, name) : ''}
                />
            );
        case AttributeType.DATE:
        case AttributeType.DATE_ARRAY: {
            const currentVal = get(values, name);
            const isRelative = typeof currentVal === 'string' && RELATIVE_CURRENT_REGEX.test(currentVal || '');
            return (
                !isRelative && (
                    <LocalizationProvider dateAdapter={AdapterMoment}>
                        <DatePicker
                            value={dateFnsParse(get(values, name), 'yyyy-MM-dd', new Date())}
                            onChange={(newValue) => {
                                handleChange({
                                    target: { name, value: newValue ? formatDate(newValue.toUTCString()) : newValue },
                                });
                            }}
                            inputFormat={useGlobalConfig.getState().getDateFormat()}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    size="small"
                                    InputLabelProps={{ shrink: true }}
                                    error={isError(formProps, name)}
                                    helperText={isError(formProps, name) ? get(errors, name) : ''}
                                />
                            )}
                        />
                    </LocalizationProvider>
                )
            );
        }
        case AttributeType.DATE_TIME:
        case AttributeType.DATE_TIME_ARRAY: {
            const currentVal = get(values, name);
            const isRelative = typeof currentVal === 'string' && RELATIVE_CURRENT_REGEX.test(currentVal || '');

            return (
                !isRelative && (
                    <LocalizationProvider dateAdapter={AdapterMoment}>
                        <DateTimePicker
                            inputFormat={useGlobalConfig.getState().getDateTimeFormat()}
                            value={get(values, name)}
                            onChange={(newValue) => {
                                handleChange({
                                    target: { name, value: newValue ? formatSystemDateTime(newValue) : newValue },
                                });
                            }}
                            disabled={false}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    size="small"
                                    InputLabelProps={{ shrink: true }}
                                    error={isError(formProps, name)}
                                    helperText={isError(formProps, name) ? get(errors, name) : ''}
                                />
                            )}
                        />
                    </LocalizationProvider>
                )
            );
        }
        default:
            return (
                <TextField
                    size="small"
                    name={name}
                    value={get(values, name)}
                    placeholder="Input value"
                    onChange={handleChange}
                    error={isError(formProps, name)}
                    helperText={isError(formProps, name) ? get(errors, name) : ''}
                />
            );
    }
};
export const Filter = ({
    name,
    formProps,
    schemaDetail,
    fieldPrefix = '',
    filterOptions,
    selectedRelation,
}: {
    name: string;
    formProps: FormikProps<AdvancedFilterFormProps>;
    schemaDetail: SchemaWithLifeCycleDetail;
    fieldPrefix?: string;
    filterOptions?: any[];
    selectedRelation?: RelationRowData;
}) => {
    const { errors, values } = formProps;
    const filter = get(values, name);
    const filterType: FilterType = get(filter, 'filterType', '');
    return (
        <Box sx={{ display: 'flex', gap: '8px', position: 'relative', width: '100%' }}>
            <FormControl error={isError(formProps, `${name}.filterType`)}>
                <Select
                    size="small"
                    name={`${name}.filterType`}
                    MenuProps={MENU_PROPS}
                    inputProps={{ 'aria-label': 'Without label' }}
                    onChange={handleFilterTypeChange(formProps, name, fieldPrefix)}
                    displayEmpty
                    value={filterType}
                    renderValue={(selected: any) => {
                        return selected ? FILTER_OPTION_LABEL[selected] : 'Select Filter';
                    }}
                >
                    {(filterOptions || FILTER_OPTIONS).map(({ value, label }) => (
                        <MenuItem value={value} key={`filter-${value}`}>
                            {label}
                        </MenuItem>
                    ))}
                </Select>
                {isError(formProps, `${name}.filterType`) && (
                    <FormHelperText>{get(errors, `${name}.filterType`)}</FormHelperText>
                )}
            </FormControl>
            {isAttributeFilter(filterType) && (
                <AttributeFilter
                    fieldPrefix={fieldPrefix}
                    formProps={formProps}
                    name={name}
                    schemaDetail={schemaDetail}
                />
            )}
            {isRelationAttributeFilter(filterType) && (
                <RelationAttributeFilter
                    fieldPrefix={`${fieldPrefix}relationAttr:`}
                    formProps={formProps}
                    name={name}
                    schemaDetail={schemaDetail}
                    selectedRelation={selectedRelation}
                />
            )}

            {isRelationFilter(filterType) && (
                <>
                    <RelationFilter formProps={formProps} name={name} schemaDetail={schemaDetail} />
                </>
            )}

            {isLifecycleStateFilter(filterType) && (
                <LifecycleStateFilter name={name} formProps={formProps} schemaDetail={schemaDetail} />
            )}

            {isClassificationFilter(filterType) && (
                <ClassificationFilter
                    fieldPrefix={fieldPrefix}
                    formProps={formProps}
                    name={name}
                    schemaDetail={schemaDetail}
                />
            )}
        </Box>
    );
};
