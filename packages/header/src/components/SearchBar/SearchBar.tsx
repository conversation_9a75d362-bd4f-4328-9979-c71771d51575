/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Autocomplete, Box, Button, ClickAwayListener, styled, Typography } from '@mui/material';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AdvanceSearchIcon, Loading, useDebounce } from '@glidesystems/styleguide';

import {
    entityUrls,
    buildContainsQuery,
    buildOrOperatorQuery,
    buildInQuery,
    buildAndOperatorQuery,
    buildExactQuery,
    buildRegexQuery,
    genAiUrls,
    fetch,
    SYSTEM_ENTITY_TYPE,
    AttributeType,
} from '@glidesystems/api';
import { useSchemaDetail, useAuth, useFilters, useSchemaTree } from '@glidesystems/caching-store';
import { matchSorter } from 'match-sorter';
import { useNavigate } from 'react-router-dom';
import {
    BooleanFalseType,
    BooleanTrueType,
    BOOLEAN_FALSE_VALUES,
    BOOLEAN_TRUE_VALUES,
    CUSTOM_EVENTS,
    DEBOUNCE_THRESHOLD,
    DEFAULT_OPTIONS,
    FILTER_CONFIG,
    GROUP_LABEL,
    KEYWORD_PREFIX,
    OptionType,
    RELATION_ATTRIBUTE_KEYWORD,
    RELATION_KEYWORD,
    SUPPORTED_QUICK_FILTERS,
    TYPE_KEYWORD,
} from '../../constants';
import {
    buildAttributeOptions,
    buildRelationAttributeOptions,
    combineAppliedFilterOptions,
    containsKeyword,
    formatFilterText,
    getEmptyResultOption,
    isAttributeKeyword,
    isEntityTypeKeyword,
    isRelationAttributeKeyword,
    isRelationKeyword,
    isValidSearch,
} from '../../utils/helper';
import {
    AppliedFilterOptionRenderer,
    FilterGroupRenderer,
    FilterTag,
    ListBoxComponent,
    NoResultRenderer,
    OptionListItem,
    PopperMenuRender,
    ResultLink,
    SearchInputRenderer,
} from './Renderer';
import useLocalStore from '../../store';
import useRecentlyViews from '../../hooks/useRecentlyViews';
import useAvailableAttributes from '../../hooks/useAvailableAttributes';
import AdvancedSearchModal, { useAdvancedSearchToggle } from '../AdvancedSearch/AdvancedSearchModal';
import useToggle from '../../hooks/useToggle';
import CreateAndEditBookmarks from '../Bookmarks/CreateAndEditBookmarks';
import { EditSaveFilter } from '../SaveFilter/EditSaveFilter';
import { SharedSaveFilter } from '../SaveFilter/ShareSaveFilter';

const SearchBarWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    margin: '0 24px',
    marginLeft: 0,
    position: 'relative',
    '& .advanceSearchBtn': {
        height: '36px',
        width: '161px',
        minWidth: '161px',
        borderTopRightRadius: '4px',
        borderBottomRightRadius: '4px',
        position: 'absolute',
        right: 0,
        [theme.breakpoints.down('md')]: {
            '& .advanceSearchText': {
                display: 'none',
            },
            width: '36px',
            minWidth: '36px',
            '& .MuiButton-endIcon': {
                marginLeft: 0,
            },
        },
    },
    '& .searchBox': {
        backgroundColor: '#606A7F',
        borderTopLeftRadius: '4px',
        borderBottomLeftRadius: '4px',
        height: '36px',
        '& input': {
            padding: '8px 14px',
            paddingLeft: '4px !important',
            color: theme.palette.glide.text.normal.tertiary,
            '&::placeholder': {
                color: theme.palette.glide.text.normal.tertiary,
                opacity: 1,
                weight: 500,
                fontSize: '14px',
            },
        },
        '& fieldset': {
            border: 'none !important',
            borderTopLeftRadius: '4px',
            borderBottomLeftRadius: '4px',
        },
        '& .MuiOutlinedInput-root': {
            flexWrap: 'nowrap',
        },
    },
    '& .text': {
        color: theme.palette.glide.text.normal.tertiary,
        fontSize: '12px',
        fontWeight: 500,
    },
    '& .startIcon': {
        margin: '0 8px',
    },
    '& .filterTag': {
        height: '20px',
        backgroundColor: theme.palette.glide.background.normal.inverseSecondary,
        borderRadius: '4px',
        color: theme.palette.glide.text.normal.inverseTertiary,
        fontWeight: 500,
        '& .MuiChip-label': {
            padding: '4px',
        },
    },
    '& .MuiAutocomplete-endAdornment': {
        '& button': {
            color: theme.palette.glide.text.normal.tertiary,
            height: '24px',
            width: '24px',
            marginTop: '2px',
        },
        '& .closeIcon': {
            height: '16px',
            width: '16px',
        },
    },
    '& .startAdornment': {
        maxWidth: '30%',
    },
}));

const SearchBar = () => {
    const [searchText, setSearchText] = useState('');
    const debouncedSearchText = useDebounce(searchText, DEBOUNCE_THRESHOLD);
    const [enabledAdvancedSearch, setEnabledAdvancedSearch] = useState(false);
    const debouncedAdvancedToggle = useDebounce(enabledAdvancedSearch, DEBOUNCE_THRESHOLD);
    const [fetchedOptions, setFetchedOptions] = useState([]);
    const [loadingOptions, setLoadingOptions] = useState(false);
    const [open, setOpen] = useState(false);
    const [autocompleteValue, _] = useState([]);
    const inputRef = useRef(null);
    const { schema, getSchema } = useSchemaDetail();
    const { schemaTreeMap, relationTreeMap, defaultAttributes } = useSchemaTree();
    const { recentlyViewedEntities } = useLocalStore();
    const userInfo = useAuth((state) => state.userInfo);
    const [appliedFilters, addFilter, removeFilter] = useFilters((state) => [
        state.appliedFilters,
        state.addFilter,
        state.removeFilter,
    ]);
    const [, advancedSearchToggle] = useAdvancedSearchToggle();
    const navigate = useNavigate();

    const availableAttributes = useAvailableAttributes(appliedFilters, schema, defaultAttributes);

    const schemaOptions = schemaTreeMap
        ? Object.values(schemaTreeMap)
              .filter((schema) => schema.visible)
              .map(({ name, displayName }: any) => ({
                  value: `${TYPE_KEYWORD} ${name}`,
                  label: displayName,
                  type: OptionType.Schema,
                  group: GROUP_LABEL[OptionType.Schema],
                  originalValue: name,
                  filterCompare: `${TYPE_KEYWORD} ${displayName}`,
              }))
        : [];

    const combineDefaultWithRecentlyViewed = (recentlyViewedEntities) => DEFAULT_OPTIONS.concat(recentlyViewedEntities);

    const getDisplayOptions = useCallback(
        (searchText: string, schemaOptions, availableAttributes, fetchedOptions, recentlyViewedEntities) => {
            if (isEntityTypeKeyword(searchText)) {
                return schemaOptions;
            }
            if (isAttributeKeyword(searchText)) {
                return availableAttributes;
            }
            if (isRelationAttributeKeyword(searchText)) {
                // Format: /RelationAttribute RELATION_NAME relationAttr
                // We cannot put RELATION_DISPLAY_NAME (contains spaces) instead of RELATION_NAME as it would be hard to identify
                // where the RELATION_DISPLAY_NAME string ends and where the relationAttr string begins
                const appliedRelation = searchText.replace(RELATION_ATTRIBUTE_KEYWORD, '').trim().split(' ')[0];
                if (appliedRelation?.length > 0 && relationTreeMap[appliedRelation]) {
                    return buildRelationAttributeOptions(
                        relationTreeMap[appliedRelation].toEntityType,
                        schema,
                        appliedRelation,
                        relationTreeMap[appliedRelation].displayName
                    );
                }
                return [];
            }
            if (searchText.startsWith(KEYWORD_PREFIX)) {
                return SUPPORTED_QUICK_FILTERS;
            }

            if (searchText.length >= 2 || searchText === '*') {
                return fetchedOptions;
            }
            return combineDefaultWithRecentlyViewed(recentlyViewedEntities);
        },
        [relationTreeMap, schema]
    );

    const getBasicSearchResult = useCallback(async (appliedFilters, searchText: string, signal) => {
        try {
            setLoadingOptions(true);
            setFetchedOptions([]);
            let fields = ['revision', 'isMaster', 'isBookmarked'];
            let attributeQueries = [];
            let schemaTypes = [];
            const containsWildcard = searchText.includes('*') || searchText.includes('?');
            appliedFilters.forEach((filter) => {
                if (
                    filter.type === OptionType.Attribute ||
                    filter.type === OptionType.State ||
                    filter.type === OptionType.RelationAttribute
                ) {
                    const isText = [AttributeType.STRING, AttributeType.TEXT].includes(filter.attribute.type);
                    if (filter.type === OptionType.Attribute) {
                        fields.push(filter.originalValue);
                    }
                    if (filter.attribute.type === AttributeType.BOOLEAN) {
                        if (BOOLEAN_TRUE_VALUES.includes(searchText.toLowerCase() as BooleanTrueType)) {
                            attributeQueries.push(buildExactQuery(filter.originalValue, true));
                        } else if (BOOLEAN_FALSE_VALUES.includes(searchText.toLowerCase() as BooleanFalseType)) {
                            attributeQueries.push(buildExactQuery(filter.originalValue, false));
                        }
                        return;
                    }
                    if (isText) {
                        attributeQueries.push(
                            containsWildcard
                                ? buildRegexQuery(filter.originalValue, searchText)
                                : buildContainsQuery(filter.originalValue, searchText)
                        );
                        return;
                    }
                    if (isNaN(Number(searchText))) {
                        return;
                    }
                    attributeQueries.push(buildExactQuery(filter.originalValue, Number(searchText)));
                    return;
                }
                if (filter.type === OptionType.Schema) {
                    schemaTypes.push(filter.originalValue);
                }
            });
            const queries = [];
            if (schemaTypes.length > 0) {
                queries.push(buildInQuery('schemaType', schemaTypes));
            }
            if (attributeQueries.length > 0) {
                queries.push(
                    attributeQueries.length === 1 ? attributeQueries[0] : buildOrOperatorQuery(attributeQueries)
                );
            } else {
                if (!(searchText === '*')) {
                    queries.push(
                        buildOrOperatorQuery(
                            containsWildcard
                                ? [buildRegexQuery('name', searchText), buildRegexQuery('description', searchText)]
                                : [
                                      buildContainsQuery('name', searchText),
                                      buildContainsQuery('description', searchText),
                                  ]
                        )
                    );
                }
            }
            const query = JSON.stringify(buildAndOperatorQuery(queries));
            const {
                data: { data },
            } = await fetch({
                ...(schemaTypes.length > 1 ? entityUrls.getListEntitySysRoot : entityUrls.getListEntity),
                params: {
                    entityType: schemaTypes.length > 1 ? undefined : schemaTypes[0] || SYSTEM_ENTITY_TYPE.SYS_ROOT,
                },
                qs: {
                    query,
                    offset: 0,
                    limit: 40,
                    fields,
                },
                signal,
            });
            setFetchedOptions(
                data.map((entity) => ({
                    value: entity.id,
                    label: entity.properties.name,
                    group: GROUP_LABEL[OptionType.Result],
                    type: OptionType.Result,
                    entity,
                }))
            );
        } catch (err) {
            setFetchedOptions([]);
        } finally {
            setLoadingOptions(false);
        }
    }, []);

    /**
     * NLP Search
     */
    const getAdvancedSearchResult = useCallback(async (appliedFilters, searchText, signal) => {
        const entityTypes = appliedFilters
            .filter((filter) => filter.type === OptionType.Schema)
            .map((filter) => filter.originalValue);
        try {
            setLoadingOptions(true);
            const { data } = await fetch({
                ...genAiUrls.search,
                qs: {
                    entityTypes,
                    search: searchText,
                },
                skipToast: true,
                signal,
            });
            setFetchedOptions(
                data.map((entity) => ({
                    value: entity.id,
                    label: entity.properties.name,
                    group: GROUP_LABEL[OptionType.Result],
                    type: OptionType.Result,
                    entity,
                }))
            );
        } catch (err) {
            setFetchedOptions([]);
        } finally {
            setLoadingOptions(false);
        }
    }, []);

    const searchOptions = useMemo(() => {
        const displayOptions = getDisplayOptions(
            debouncedSearchText,
            schemaOptions,
            availableAttributes,
            fetchedOptions,
            recentlyViewedEntities
        );

        return loadingOptions
            ? []
            : displayOptions.length === 0
            ? combineAppliedFilterOptions(getEmptyResultOption(debouncedSearchText), appliedFilters)
            : combineAppliedFilterOptions(displayOptions, appliedFilters);
    }, [
        fetchedOptions,
        debouncedSearchText,
        loadingOptions,
        appliedFilters,
        schemaOptions,
        availableAttributes,
        recentlyViewedEntities,
    ]);

    const handleSearchTextChange = useCallback((e, _, reason) => {
        if (reason === 'reset') return;
        setSearchText(e?.target?.value || '');
    }, []);

    /**
     * Using fuzzy search
     */
    const filterOptions = useCallback(() => {
        const displayOptions = getDisplayOptions(
            debouncedSearchText,
            schemaOptions,
            availableAttributes,
            fetchedOptions,
            recentlyViewedEntities
        );
        const filteredResults = matchSorter(displayOptions, formatFilterText(debouncedSearchText), FILTER_CONFIG);

        // TODO: refactor this logic, add new empty option for keywords
        return isValidSearch(debouncedSearchText)
            ? loadingOptions
                ? []
                : displayOptions.length === 0
                ? combineAppliedFilterOptions(getEmptyResultOption(debouncedSearchText), appliedFilters)
                : combineAppliedFilterOptions(displayOptions, appliedFilters)
            : combineAppliedFilterOptions(
                  filteredResults.length > 0 ? filteredResults : getEmptyResultOption(debouncedSearchText),
                  appliedFilters
              );
    }, [
        debouncedSearchText,
        appliedFilters,
        searchOptions,
        loadingOptions,
        schemaOptions,
        availableAttributes,
        fetchedOptions,
    ]);

    const handleSelectionChange = useCallback((e, newValue, reason, details) => {
        const newOption = details.option;

        switch (newOption.type) {
            case OptionType.Keyword:
                setSearchText(`${newOption.value} `);
                return;
            case OptionType.Tip:
                if (containsKeyword(newOption.value)) {
                    setSearchText(`${newOption.value}`);
                } else {
                    addFilter(newOption.item);
                }
                return;
            case OptionType.Result:
            case OptionType.RecentlyViewed:
                setOpen(false);
                inputRef.current.blur();
                return;
            case OptionType.Relation:
                // for relation we'll add filter only after relation attribute is chosen
                setLoadingOptions(true);
                getSchema(newOption.relation.toEntityType).then(() => setLoadingOptions(false));
                setSearchText(`${RELATION_ATTRIBUTE_KEYWORD} ${newOption.value}`);
                return;
            default:
                addFilter(newOption);
                setSearchText('');
                return;
        }
    }, []);

    const updateBookmark = useCallback(
        (id: string, isBookmarked: boolean) => {
            const updatedOptions = fetchedOptions.map((option) => {
                if (option?.entity?.id === id) {
                    return {
                        ...option,
                        entity: {
                            ...option.entity,
                            properties: {
                                ...option.entity.properties,
                                isBookmarked: isBookmarked,
                            },
                        },
                    };
                }
                return option;
            });
            setFetchedOptions(updatedOptions);
        },
        [fetchedOptions]
    );

    const renderOption = useCallback(
        (props, option) => {
            const optionType = option.type;
            switch (optionType) {
                case OptionType.SavedFilter:
                    return <FilterTag size="small" label={option.label} key={props.key} />;
                case OptionType.AppliedFilter:
                    return (
                        <AppliedFilterOptionRenderer option={option} key={props.key} onRemoveFilter={removeFilter} />
                    );
                case OptionType.Empty:
                    return <NoResultRenderer key={props.key} {...props} option={option} />;
                case OptionType.Result:
                case OptionType.RecentlyViewed:
                    return <ResultLink {...props} key={props.key} option={option} onBookmarkUpdate={updateBookmark} />;
                default:
                    return (
                        <OptionListItem {...props}>
                            <Typography className="item">
                                {option.label}
                                {option.description && <span className="description">{option.description}</span>}
                            </Typography>
                        </OptionListItem>
                    );
            }
        },
        [updateBookmark]
    );

    const handleKeyDown = useCallback(
        (e) => {
            if (e.key === 'Enter') {
                if (isValidSearch(searchText)) {
                    const types = appliedFilters
                        .filter((filter) => filter.type === OptionType.Schema)
                        .map((type) => type.originalValue);
                    setOpen(false);
                    navigate(
                        `/search?query=${searchText}&types=${JSON.stringify(types)}${
                            enabledAdvancedSearch ? '&searchType=advanced' : '&searchType=basic'
                        }`
                    );
                    inputRef.current.blur();
                }
            }
        },
        [searchText, enabledAdvancedSearch]
    );

    const toggleAdvancedSearch = useCallback(() => {
        setEnabledAdvancedSearch((prev) => !prev);
    }, [setEnabledAdvancedSearch]);

    useEffect(() => {
        appliedFilters.forEach((option) => {
            if (option.type === OptionType.Schema) {
                getSchema(option.originalValue);
            }
        });
    }, [appliedFilters]);

    useEffect(() => {
        const controller = new AbortController();
        if (isValidSearch(debouncedSearchText)) {
            if (debouncedAdvancedToggle) {
                getAdvancedSearchResult(appliedFilters, debouncedSearchText, controller.signal);
            }
            getBasicSearchResult(appliedFilters, debouncedSearchText, controller.signal);
        }
        return () => {
            controller.abort();
        };
    }, [appliedFilters, debouncedSearchText, debouncedAdvancedToggle]);

    useEffect(() => {
        const toggleSearchBar = (e) => {
            if (e.detail?.open !== undefined && e.detail?.open !== null) {
                setOpen(e.detail.open);
            }
        };
        window.addEventListener(CUSTOM_EVENTS.TOGGLE_SEARCH_BAR, toggleSearchBar);
        return () => {
            window.removeEventListener(CUSTOM_EVENTS.TOGGLE_SEARCH_BAR, toggleSearchBar);
        };
    }, []);

    useRecentlyViews(userInfo);

    const showLoading =
        recentlyViewedEntities === null || ((!schemaTreeMap || loadingOptions) && !containsKeyword(searchText));

    const PopperComponent = useMemo(
        () => (props) =>
            (
                <PopperMenuRender
                    {...props}
                    toggleAdvancedSearch={() => {
                        setOpen(false);
                        advancedSearchToggle.open();
                    }}
                />
            ),
        [advancedSearchToggle]
    );

    return (
        <ClickAwayListener onClickAway={() => setOpen(false)}>
            <SearchBarWrapper className="dim-when-disabled none-pe-when-disabled">
                <Autocomplete
                    ListboxComponent={ListBoxComponent}
                    options={searchOptions}
                    inputValue={searchText}
                    onInputChange={handleSearchTextChange}
                    open={open}
                    onFocus={(e) => {
                        setOpen(true);
                    }}
                    fullWidth
                    loading={showLoading}
                    loadingText={<Loading />}
                    value={autocompleteValue}
                    onKeyDown={handleKeyDown}
                    disableCloseOnSelect
                    onChange={handleSelectionChange}
                    size="small"
                    getOptionLabel={(option) => option.label}
                    renderInput={(props) => <SearchInputRenderer {...props} inputRef={inputRef} />}
                    filterOptions={filterOptions}
                    PopperComponent={PopperComponent}
                    groupBy={(option) => option.group}
                    renderGroup={FilterGroupRenderer}
                    renderOption={renderOption}
                    multiple
                    slotProps={{
                        paper: {
                            sx: { overflow: 'hidden' },
                        },
                    }}
                    ListboxProps={{
                        style: {
                            maxHeight: '80vh', // mui default is 40vh
                        },
                    }}
                />
                <Button
                    size="small"
                    variant="contained-white"
                    className={`advanceSearchBtn ${enabledAdvancedSearch ? 'advanced' : 'basic'}`}
                    endIcon={<AdvanceSearchIcon />}
                    onClick={toggleAdvancedSearch}
                >
                    <span className="advanceSearchText toggle">
                        {enabledAdvancedSearch ? 'Ask AI' : 'Basic Search'}
                    </span>
                </Button>
                <AdvancedSearchModal />
                <EditSaveFilter />
                <SharedSaveFilter />
                <CreateAndEditBookmarks postSubmit={updateBookmark} />
            </SearchBarWrapper>
        </ClickAwayListener>
    );
};

export default SearchBar;
